import pygame
import math
import random
import sys
from enum import Enum
from dataclasses import dataclass
from typing import List, Tuple, Optional

# 初始化pygame
pygame.init()

# 游戏常量
SCREEN_WIDTH = 1200
SCREEN_HEIGHT = 800
FPS = 60

# 颜色定义
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)
RED = (255, 0, 0)
BLUE = (0, 0, 255)
GREEN = (0, 255, 0)
YELLOW = (255, 255, 0)
ORANGE = (255, 165, 0)
PURPLE = (128, 0, 128)
GRAY = (128, 128, 128)
DARK_GRAY = (64, 64, 64)

class WeaponType(Enum):
    PISTOL = "pistol"
    SHOTGUN = "shotgun"
    MACHINE_GUN = "machine_gun"
    SNIPER = "sniper"
    PENETRATOR = "penetrator"  # 穿透武器
    BOUNCER = "bouncer"  # 反弹武器
    ONE_SHOT = "one_shot"  # 高伤武器
    GATLING = "gatling"  # 加特林
    SUPER_WEAPON = "super_weapon"  # 高强武器
    TELEPORT_GUN = "teleport_gun"  # 传送枪

class PowerUpType(Enum):
    HEALTH = "health"
    ARMOR = "armor"
    AMMO = "ammo"
    WEAPON_UPGRADE = "weapon_upgrade"
    TRACKING = "tracking"  # 追踪增益

@dataclass
class WeaponStats:
    damage: int
    fire_rate: int  # 毫秒间隔
    bullet_speed: int
    max_ammo: int
    reload_time: int  # 毫秒
    spread: float  # 散射角度
    bullets_per_shot: int = 1
    penetration: int = 0  # 穿透层数
    bounce_count: int = 0  # 反弹次数
    bullet_size: float = 1.0  # 子弹大小倍数
    infinite_speed: bool = False  # 无限速度（狙击枪）
    ignore_walls: bool = False  # 无视墙壁（传送枪）
    teleport_on_hit: bool = False  # 击中后传送

class Vector2:
    def __init__(self, x: float = 0, y: float = 0):
        self.x = x
        self.y = y
    
    def __add__(self, other):
        return Vector2(self.x + other.x, self.y + other.y)
    
    def __sub__(self, other):
        return Vector2(self.x - other.x, self.y - other.y)
    
    def __mul__(self, scalar):
        return Vector2(self.x * scalar, self.y * scalar)
    
    def length(self):
        return math.sqrt(self.x ** 2 + self.y ** 2)
    
    def normalize(self):
        length = self.length()
        if length > 0:
            return Vector2(self.x / length, self.y / length)
        return Vector2(0, 0)
    
    def to_tuple(self):
        return (int(self.x), int(self.y))

class Bullet:
    def __init__(self, pos: Vector2, direction: Vector2, speed: int, damage: int, owner_id: int,
                 penetration: int = 0, bounce_count: int = 0, bullet_size: float = 1.0,
                 is_super_weapon: bool = False, infinite_speed: bool = False,
                 ignore_walls: bool = False, teleport_on_hit: bool = False,
                 is_tracking: bool = False, target_player_id: int = -1):
        self.pos = pos
        self.velocity = direction.normalize() * speed
        self.damage = damage
        self.owner_id = owner_id
        self.radius = int(3 * bullet_size)
        self.lifetime = 3000  # 3秒生命周期
        self.birth_time = pygame.time.get_ticks()
        self.penetration = penetration
        self.bounce_count = bounce_count
        self.bullet_size = bullet_size
        self.penetrated_walls = 0
        self.bounces_used = 0
        self.is_super_weapon = is_super_weapon
        self.infinite_speed = infinite_speed
        self.ignore_walls = ignore_walls
        self.teleport_on_hit = teleport_on_hit
        self.is_tracking = is_tracking
        self.target_player_id = target_player_id
        self.trail_points = []  # 狙击枪轨迹点
        self.size_growth_rate = 0.5 if teleport_on_hit else 0  # 传送枪子弹增长率
    
    def update(self, dt: float, players=None):
        # 追踪逻辑
        if self.is_tracking and players and self.target_player_id >= 0:
            target_player = players[self.target_player_id]
            direction_to_target = target_player.pos - self.pos
            if direction_to_target.length() > 0:
                # 缓慢调整方向朝向目标
                current_direction = self.velocity.normalize()
                target_direction = direction_to_target.normalize()

                # 混合当前方向和目标方向
                mix_factor = 0.05  # 追踪强度
                new_direction = current_direction * (1 - mix_factor) + target_direction * mix_factor
                self.velocity = new_direction.normalize() * self.velocity.length()

        # 传送枪子弹大小增长
        if self.teleport_on_hit:
            age = (pygame.time.get_ticks() - self.birth_time) / 1000.0
            self.bullet_size = 1.0 + age * self.size_growth_rate
            self.radius = int(3 * self.bullet_size)

        # 狙击枪轨迹记录
        if self.infinite_speed:
            self.trail_points.append(Vector2(self.pos.x, self.pos.y))
            if len(self.trail_points) > 20:  # 限制轨迹点数量
                self.trail_points.pop(0)

        self.pos = self.pos + self.velocity * dt
        return pygame.time.get_ticks() - self.birth_time < self.lifetime
    
    def draw(self, screen):
        # 狙击枪轨迹绘制
        if self.infinite_speed and len(self.trail_points) > 1:
            for i in range(len(self.trail_points) - 1):
                alpha = int(255 * (i + 1) / len(self.trail_points))
                color = (255, 255, 255, alpha)
                pygame.draw.line(screen, WHITE, self.trail_points[i].to_tuple(),
                               self.trail_points[i + 1].to_tuple(), 3)

        # 根据子弹类型选择颜色
        color = YELLOW
        if self.teleport_on_hit:
            # 传送枪：蓝色渐变
            color = (0, 100, 255)
        elif self.is_tracking:
            # 追踪子弹：红色闪烁
            flash = (pygame.time.get_ticks() // 150) % 2
            color = (255, 100, 100) if flash else (255, 0, 0)
        elif self.is_super_weapon:
            # Super weapon子弹：金色带闪烁效果
            flash = (pygame.time.get_ticks() // 100) % 2
            color = (255, 215, 0) if flash else (255, 255, 0)  # 金色闪烁
        elif self.infinite_speed:
            # 狙击枪：白色
            color = WHITE
        elif self.penetration > 0 and self.bounce_count > 0:
            # 既能穿透又能反弹的子弹：青色
            color = (0, 255, 255)
        elif self.penetration > 0:
            color = GREEN  # 穿透子弹为绿色
        elif self.bounce_count > 0:
            color = PURPLE  # 反弹子弹为紫色
        elif self.bullet_size > 2:
            color = RED  # 高伤子弹为红色

        pygame.draw.circle(screen, color, self.pos.to_tuple(), self.radius)
        if self.bullet_size > 1 or self.is_super_weapon or self.teleport_on_hit:
            pygame.draw.circle(screen, WHITE, self.pos.to_tuple(), self.radius, 2)
    
    def get_rect(self):
        return pygame.Rect(self.pos.x - self.radius, self.pos.y - self.radius, 
                          self.radius * 2, self.radius * 2)

class Weapon:
    def __init__(self, weapon_type: WeaponType):
        self.type = weapon_type
        self.stats = self._get_weapon_stats(weapon_type)
        self.current_ammo = self.stats.max_ammo
        self.last_shot_time = 0
        self.reload_start_time = 0
        self.is_reloading = False
    
    def _get_weapon_stats(self, weapon_type: WeaponType) -> WeaponStats:
        weapon_configs = {
            WeaponType.PISTOL: WeaponStats(25, 300, 400, 12, 1500, 0.1),
            WeaponType.SHOTGUN: WeaponStats(18, 800, 300, 8, 2000, 0.3, 5),
            WeaponType.MACHINE_GUN: WeaponStats(22, 120, 350, 30, 2500, 0.15),
            WeaponType.SNIPER: WeaponStats(75, 1500, 9999, 5, 3000, 0.05, 1, 0, 0, 1.0, True),  # 无限速度
            WeaponType.PENETRATOR: WeaponStats(28, 400, 450, 10, 2000, 0.08, 1, random.randint(1, 2)),
            WeaponType.BOUNCER: WeaponStats(30, 500, 400, 8, 2200, 0.12, 1, 0, random.randint(2, 4)),
            WeaponType.ONE_SHOT: WeaponStats(100, 1000, 500, 1, 7000, 0.02, 1, 0, 0, 3.0),
            WeaponType.GATLING: WeaponStats(8, 80, 380, 300, 3000, 0.2),
            WeaponType.SUPER_WEAPON: WeaponStats(50, 350, 480, 5, 2300, 0.06, 1, 1, 1),
            WeaponType.TELEPORT_GUN: WeaponStats(0, 800, 300, 3, 2500, 0.05, 1, 0, 0, 1.0, False, True, True)  # 传送枪
        }
        return weapon_configs[weapon_type]
    
    def can_shoot(self) -> bool:
        current_time = pygame.time.get_ticks()
        return (not self.is_reloading and 
                self.current_ammo > 0 and 
                current_time - self.last_shot_time >= self.stats.fire_rate)
    
    def shoot(self, pos: Vector2, direction: Vector2, owner_id: int, is_tracking: bool = False, target_player_id: int = -1) -> List[Bullet]:
        if not self.can_shoot():
            return []

        bullets = []
        current_time = pygame.time.get_ticks()

        for i in range(self.stats.bullets_per_shot):
            # 添加散射
            spread_angle = (random.random() - 0.5) * self.stats.spread
            angle = math.atan2(direction.y, direction.x) + spread_angle
            bullet_direction = Vector2(math.cos(angle), math.sin(angle))

            is_super = self.type == WeaponType.SUPER_WEAPON
            bullet = Bullet(Vector2(pos.x, pos.y), bullet_direction,
                          self.stats.bullet_speed, self.stats.damage, owner_id,
                          self.stats.penetration, self.stats.bounce_count, self.stats.bullet_size,
                          is_super, self.stats.infinite_speed, self.stats.ignore_walls,
                          self.stats.teleport_on_hit, is_tracking, target_player_id)
            bullets.append(bullet)

        self.current_ammo -= 1
        self.last_shot_time = current_time

        return bullets
    
    def start_reload(self):
        if self.current_ammo < self.stats.max_ammo and not self.is_reloading:
            self.is_reloading = True
            self.reload_start_time = pygame.time.get_ticks()
    
    def update(self):
        if self.is_reloading:
            if pygame.time.get_ticks() - self.reload_start_time >= self.stats.reload_time:
                self.current_ammo = self.stats.max_ammo
                self.is_reloading = False

class PowerUp:
    def __init__(self, pos: Vector2, power_type: PowerUpType):
        self.pos = pos
        self.type = power_type
        self.radius = 15
        self.spawn_time = pygame.time.get_ticks()
        self.lifetime = 30000  # 30秒后消失
        self.bob_offset = 0
    
    def update(self, dt: float):
        self.bob_offset += dt * 0.003
        return pygame.time.get_ticks() - self.spawn_time < self.lifetime
    
    def draw(self, screen):
        colors = {
            PowerUpType.HEALTH: RED,
            PowerUpType.ARMOR: BLUE,
            PowerUpType.AMMO: GREEN,
            PowerUpType.WEAPON_UPGRADE: PURPLE,
            PowerUpType.TRACKING: ORANGE
        }

        symbols = {
            PowerUpType.HEALTH: "+",
            PowerUpType.ARMOR: "A",
            PowerUpType.AMMO: "∞",
            PowerUpType.WEAPON_UPGRADE: "W",
            PowerUpType.TRACKING: "T"
        }

        bob_y = self.pos.y + math.sin(self.bob_offset) * 5

        # 绘制道具圆圈
        pygame.draw.circle(screen, colors[self.type],
                          (int(self.pos.x), int(bob_y)), self.radius)
        pygame.draw.circle(screen, WHITE,
                          (int(self.pos.x), int(bob_y)), self.radius, 2)

        # 绘制符号
        font = pygame.font.Font(None, 24)
        symbol_surface = font.render(symbols[self.type], True, WHITE)
        symbol_rect = symbol_surface.get_rect(center=(int(self.pos.x), int(bob_y)))
        screen.blit(symbol_surface, symbol_rect)
    
    def get_rect(self):
        return pygame.Rect(self.pos.x - self.radius, self.pos.y - self.radius,
                          self.radius * 2, self.radius * 2)

class Wall:
    def __init__(self, x: int, y: int, width: int, height: int):
        self.rect = pygame.Rect(x, y, width, height)

    def draw(self, screen):
        pygame.draw.rect(screen, GRAY, self.rect)
        pygame.draw.rect(screen, WHITE, self.rect, 2)

class MapManager:
    def __init__(self):
        self.maps = {
            "经典对战": self._create_classic_map,
            "迷宫战场": self._create_maze_map,
            "开放竞技场": self._create_arena_map,
            "十字路口": self._create_crossroads_map,
            "堡垒攻防": self._create_fortress_map,
            "螺旋战场": self._create_spiral_map,
            "变动战场": self._create_dynamic_map
        }
        self.current_map_name = ""
        self.spawn_points = []

    def get_random_map(self) -> Tuple[List[Wall], List[Vector2], str]:
        """随机选择一张地图并返回墙壁、出生点和地图名称"""
        map_name = random.choice(list(self.maps.keys()))
        return self.get_map(map_name)

    def get_map(self, map_name: str) -> Tuple[List[Wall], List[Vector2], str]:
        if map_name in self.maps:
            self.current_map_name = map_name
            walls, spawn_points = self.maps[map_name]()
            # 验证出生点安全性
            spawn_points = self._validate_spawn_points(walls, spawn_points)
            self.spawn_points = spawn_points
            return walls, spawn_points, map_name
        else:
            return self._create_classic_map()[0], self._create_classic_map()[1], "经典对战"

    def _validate_spawn_points(self, walls: List[Wall], spawn_points: List[Vector2]) -> List[Vector2]:
        """验证并修正出生点，确保玩家不会被困在墙壁中"""
        safe_spawn_points = []
        player_radius = 20  # 玩家半径

        for spawn_point in spawn_points:
            # 检查出生点是否与墙壁重叠
            player_rect = pygame.Rect(spawn_point.x - player_radius, spawn_point.y - player_radius,
                                    player_radius * 2, player_radius * 2)

            collision = False
            for wall in walls:
                if player_rect.colliderect(wall.rect):
                    collision = True
                    break

            if not collision:
                safe_spawn_points.append(spawn_point)
            else:
                # 寻找附近的安全位置
                safe_point = self._find_safe_spawn_point(walls, spawn_point, player_radius)
                safe_spawn_points.append(safe_point)

        return safe_spawn_points

    def _find_safe_spawn_point(self, walls: List[Wall], original_point: Vector2, player_radius: int) -> Vector2:
        """在原始出生点附近寻找安全的出生位置"""
        # 尝试在原始点周围的不同位置
        search_radius = 50
        for distance in range(player_radius + 10, search_radius, 10):
            for angle in range(0, 360, 45):
                x = original_point.x + distance * math.cos(math.radians(angle))
                y = original_point.y + distance * math.sin(math.radians(angle))

                # 确保在屏幕范围内
                if (x - player_radius < 20 or x + player_radius > SCREEN_WIDTH - 20 or
                    y - player_radius < 20 or y + player_radius > SCREEN_HEIGHT - 20):
                    continue

                test_point = Vector2(x, y)
                test_rect = pygame.Rect(x - player_radius, y - player_radius,
                                      player_radius * 2, player_radius * 2)

                # 检查是否与墙壁碰撞
                collision = False
                for wall in walls:
                    if test_rect.colliderect(wall.rect):
                        collision = True
                        break

                if not collision:
                    return test_point

        # 如果找不到安全位置，返回屏幕中央
        return Vector2(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2)

    def _create_classic_map(self) -> Tuple[List[Wall], List[Vector2]]:
        """经典对战地图 - 原始设计"""
        walls = []

        # 边界墙
        wall_thickness = 20
        walls.extend([
            Wall(0, 0, SCREEN_WIDTH, wall_thickness),
            Wall(0, SCREEN_HEIGHT - wall_thickness, SCREEN_WIDTH, wall_thickness),
            Wall(0, 0, wall_thickness, SCREEN_HEIGHT),
            Wall(SCREEN_WIDTH - wall_thickness, 0, wall_thickness, SCREEN_HEIGHT)
        ])

        # 中央障碍物
        center_x, center_y = SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2
        walls.extend([
            Wall(center_x - 100, center_y - 20, 200, 40),
            Wall(center_x - 20, center_y - 100, 40, 200),
        ])

        # 角落掩体
        walls.extend([
            Wall(150, 150, 80, 80),
            Wall(SCREEN_WIDTH - 230, 150, 80, 80),
            Wall(150, SCREEN_HEIGHT - 230, 80, 80),
            Wall(SCREEN_WIDTH - 230, SCREEN_HEIGHT - 230, 80, 80),
        ])

        spawn_points = [
            Vector2(80, SCREEN_HEIGHT // 2),  # 左侧，远离角落掩体
            Vector2(SCREEN_WIDTH - 80, SCREEN_HEIGHT // 2)  # 右侧，远离角落掩体
        ]

        return walls, spawn_points

    def _create_maze_map(self) -> Tuple[List[Wall], List[Vector2]]:
        """迷宫战场 - 复杂的迷宫布局"""
        walls = []

        # 边界墙
        wall_thickness = 20
        walls.extend([
            Wall(0, 0, SCREEN_WIDTH, wall_thickness),
            Wall(0, SCREEN_HEIGHT - wall_thickness, SCREEN_WIDTH, wall_thickness),
            Wall(0, 0, wall_thickness, SCREEN_HEIGHT),
            Wall(SCREEN_WIDTH - wall_thickness, 0, wall_thickness, SCREEN_HEIGHT)
        ])

        # 迷宫墙壁
        # 水平墙壁
        walls.extend([
            Wall(100, 100, 200, 20),
            Wall(400, 100, 200, 20),
            Wall(700, 100, 200, 20),
            Wall(200, 200, 150, 20),
            Wall(500, 200, 200, 20),
            Wall(800, 200, 150, 20),
            Wall(100, 300, 180, 20),
            Wall(350, 300, 200, 20),
            Wall(650, 300, 180, 20),
            Wall(150, 400, 200, 20),
            Wall(450, 400, 150, 20),
            Wall(700, 400, 200, 20),
            Wall(100, 500, 150, 20),
            Wall(350, 500, 250, 20),
            Wall(700, 500, 150, 20),
            Wall(200, 600, 200, 20),
            Wall(500, 600, 200, 20),
            Wall(800, 600, 150, 20),
        ])

        # 垂直墙壁
        walls.extend([
            Wall(150, 120, 20, 100),
            Wall(300, 50, 20, 120),
            Wall(450, 120, 20, 100),
            Wall(600, 50, 20, 120),
            Wall(750, 120, 20, 100),
            Wall(900, 50, 20, 120),
            Wall(250, 220, 20, 100),
            Wall(400, 250, 20, 80),
            Wall(550, 220, 20, 100),
            Wall(700, 250, 20, 80),
            Wall(850, 220, 20, 100),
            Wall(200, 350, 20, 80),
            Wall(350, 320, 20, 100),
            Wall(500, 350, 20, 80),
            Wall(650, 320, 20, 100),
            Wall(800, 350, 20, 80),
        ])

        spawn_points = [
            Vector2(80, 200),  # 左侧安全区域
            Vector2(SCREEN_WIDTH - 80, SCREEN_HEIGHT - 200)  # 右侧安全区域
        ]

        return walls, spawn_points

    def _create_arena_map(self) -> Tuple[List[Wall], List[Vector2]]:
        """开放竞技场 - 少量掩体的开放式战场"""
        walls = []

        # 边界墙
        wall_thickness = 20
        walls.extend([
            Wall(0, 0, SCREEN_WIDTH, wall_thickness),
            Wall(0, SCREEN_HEIGHT - wall_thickness, SCREEN_WIDTH, wall_thickness),
            Wall(0, 0, wall_thickness, SCREEN_HEIGHT),
            Wall(SCREEN_WIDTH - wall_thickness, 0, wall_thickness, SCREEN_HEIGHT)
        ])

        # 中央圆形掩体（用方块近似）
        center_x, center_y = SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2
        radius = 60
        for angle in range(0, 360, 45):
            x = center_x + int(radius * math.cos(math.radians(angle))) - 15
            y = center_y + int(radius * math.sin(math.radians(angle))) - 15
            walls.append(Wall(x, y, 30, 30))

        # 四个角落的小掩体
        corner_size = 40
        walls.extend([
            Wall(100, 100, corner_size, corner_size),
            Wall(SCREEN_WIDTH - 140, 100, corner_size, corner_size),
            Wall(100, SCREEN_HEIGHT - 140, corner_size, corner_size),
            Wall(SCREEN_WIDTH - 140, SCREEN_HEIGHT - 140, corner_size, corner_size),
        ])

        # 边缘的长条掩体
        walls.extend([
            Wall(SCREEN_WIDTH // 2 - 100, 80, 200, 20),
            Wall(SCREEN_WIDTH // 2 - 100, SCREEN_HEIGHT - 100, 200, 20),
            Wall(80, SCREEN_HEIGHT // 2 - 100, 20, 200),
            Wall(SCREEN_WIDTH - 100, SCREEN_HEIGHT // 2 - 100, 20, 200),
        ])

        spawn_points = [
            Vector2(180, SCREEN_HEIGHT // 2),  # 左侧，避开边缘掩体
            Vector2(SCREEN_WIDTH - 180, SCREEN_HEIGHT // 2)  # 右侧，避开边缘掩体
        ]

        return walls, spawn_points

    def _create_crossroads_map(self) -> Tuple[List[Wall], List[Vector2]]:
        """十字路口 - 十字形通道设计"""
        walls = []

        # 边界墙
        wall_thickness = 20
        walls.extend([
            Wall(0, 0, SCREEN_WIDTH, wall_thickness),
            Wall(0, SCREEN_HEIGHT - wall_thickness, SCREEN_WIDTH, wall_thickness),
            Wall(0, 0, wall_thickness, SCREEN_HEIGHT),
            Wall(SCREEN_WIDTH - wall_thickness, 0, wall_thickness, SCREEN_HEIGHT)
        ])

        # 十字形通道的墙壁
        center_x, center_y = SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2
        corridor_width = 120

        # 上下通道的侧墙
        walls.extend([
            Wall(center_x - corridor_width // 2, 20, 20, center_y - corridor_width // 2 - 20),
            Wall(center_x + corridor_width // 2 - 20, 20, 20, center_y - corridor_width // 2 - 20),
            Wall(center_x - corridor_width // 2, center_y + corridor_width // 2, 20, SCREEN_HEIGHT - center_y - corridor_width // 2 - 20),
            Wall(center_x + corridor_width // 2 - 20, center_y + corridor_width // 2, 20, SCREEN_HEIGHT - center_y - corridor_width // 2 - 20),
        ])

        # 左右通道的侧墙
        walls.extend([
            Wall(20, center_y - corridor_width // 2, center_x - corridor_width // 2 - 20, 20),
            Wall(20, center_y + corridor_width // 2 - 20, center_x - corridor_width // 2 - 20, 20),
            Wall(center_x + corridor_width // 2, center_y - corridor_width // 2, SCREEN_WIDTH - center_x - corridor_width // 2 - 20, 20),
            Wall(center_x + corridor_width // 2, center_y + corridor_width // 2 - 20, SCREEN_WIDTH - center_x - corridor_width // 2 - 20, 20),
        ])

        # 四个角落的房间内部掩体
        room_size = 80
        walls.extend([
            Wall(80, 80, room_size, 20),
            Wall(80, 120, 20, room_size),
            Wall(SCREEN_WIDTH - 160, 80, room_size, 20),
            Wall(SCREEN_WIDTH - 100, 120, 20, room_size),
            Wall(80, SCREEN_HEIGHT - 100, room_size, 20),
            Wall(80, SCREEN_HEIGHT - 200, 20, room_size),
            Wall(SCREEN_WIDTH - 160, SCREEN_HEIGHT - 100, room_size, 20),
            Wall(SCREEN_WIDTH - 100, SCREEN_HEIGHT - 200, 20, room_size),
        ])

        spawn_points = [
            Vector2(200, 200),  # 左上房间中央
            Vector2(SCREEN_WIDTH - 200, SCREEN_HEIGHT - 200)  # 右下房间中央
        ]

        return walls, spawn_points

    def _create_fortress_map(self) -> Tuple[List[Wall], List[Vector2]]:
        """堡垒攻防 - 不对称的堡垒设计"""
        walls = []

        # 边界墙
        wall_thickness = 20
        walls.extend([
            Wall(0, 0, SCREEN_WIDTH, wall_thickness),
            Wall(0, SCREEN_HEIGHT - wall_thickness, SCREEN_WIDTH, wall_thickness),
            Wall(0, 0, wall_thickness, SCREEN_HEIGHT),
            Wall(SCREEN_WIDTH - wall_thickness, 0, wall_thickness, SCREEN_HEIGHT)
        ])

        # 左侧堡垒（玩家1）
        fortress_wall_thickness = 30
        walls.extend([
            Wall(150, 100, fortress_wall_thickness, 200),  # 左墙
            Wall(150, 100, 150, fortress_wall_thickness),  # 上墙
            Wall(150, 270, 150, fortress_wall_thickness),  # 下墙
            Wall(270, 130, fortress_wall_thickness, 140),  # 右墙（有缺口）
            Wall(200, 150, 40, 20),  # 内部掩体
            Wall(220, 200, 20, 40),  # 内部掩体
        ])

        # 右侧堡垒（玩家2）
        walls.extend([
            Wall(SCREEN_WIDTH - 180, 150, fortress_wall_thickness, 200),  # 右墙
            Wall(SCREEN_WIDTH - 330, 150, 150, fortress_wall_thickness),  # 上墙
            Wall(SCREEN_WIDTH - 330, 320, 150, fortress_wall_thickness),  # 下墙
            Wall(SCREEN_WIDTH - 330, 180, fortress_wall_thickness, 140),  # 左墙（有缺口）
            Wall(SCREEN_WIDTH - 280, 200, 40, 20),  # 内部掩体
            Wall(SCREEN_WIDTH - 260, 250, 20, 40),  # 内部掩体
        ])

        # 中央战场的掩体
        center_x = SCREEN_WIDTH // 2
        walls.extend([
            Wall(center_x - 60, 200, 120, 20),
            Wall(center_x - 20, 250, 40, 60),
            Wall(center_x - 100, 350, 200, 20),
            Wall(center_x - 20, 400, 40, 60),
        ])

        spawn_points = [
            Vector2(200, 180),  # 左侧堡垒内部，避开内部掩体
            Vector2(SCREEN_WIDTH - 250, 230)  # 右侧堡垒内部，避开内部掩体
        ]

        return walls, spawn_points

    def _create_spiral_map(self) -> Tuple[List[Wall], List[Vector2]]:
        """螺旋战场 - 螺旋形通道设计"""
        walls = []

        # 边界墙
        wall_thickness = 20
        walls.extend([
            Wall(0, 0, SCREEN_WIDTH, wall_thickness),
            Wall(0, SCREEN_HEIGHT - wall_thickness, SCREEN_WIDTH, wall_thickness),
            Wall(0, 0, wall_thickness, SCREEN_HEIGHT),
            Wall(SCREEN_WIDTH - wall_thickness, 0, wall_thickness, SCREEN_HEIGHT)
        ])

        # 螺旋形墙壁
        center_x, center_y = SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2

        # 外层螺旋
        walls.extend([
            Wall(100, 100, 400, 20),  # 上边
            Wall(480, 100, 20, 300),  # 右边
            Wall(200, 380, 300, 20),  # 下边
            Wall(200, 200, 20, 200),  # 左边
        ])

        # 中层螺旋
        walls.extend([
            Wall(700, 150, 300, 20),  # 上边
            Wall(980, 150, 20, 250),  # 右边
            Wall(750, 380, 250, 20),  # 下边
            Wall(750, 250, 20, 150),  # 左边
        ])

        # 内层螺旋
        walls.extend([
            Wall(300, 250, 150, 20),  # 上边
            Wall(430, 250, 20, 100),  # 右边
            Wall(350, 330, 100, 20),  # 下边
            Wall(350, 270, 20, 80),   # 左边
        ])

        # 连接通道
        walls.extend([
            Wall(520, 200, 160, 20),  # 连接通道1
            Wall(600, 300, 20, 100),  # 连接通道2
        ])

        spawn_points = [
            Vector2(150, 150),  # 左上角安全区域
            Vector2(SCREEN_WIDTH - 150, SCREEN_HEIGHT - 150)  # 右下角安全区域
        ]

        return walls, spawn_points

    def _create_dynamic_map(self) -> Tuple[List[Wall], List[Vector2]]:
        """变动战场 - 障碍物会随时间变化"""
        walls = []

        # 边界墙
        wall_thickness = 20
        walls.extend([
            Wall(0, 0, SCREEN_WIDTH, wall_thickness),
            Wall(0, SCREEN_HEIGHT - wall_thickness, SCREEN_WIDTH, wall_thickness),
            Wall(0, 0, wall_thickness, SCREEN_HEIGHT),
            Wall(SCREEN_WIDTH - wall_thickness, 0, wall_thickness, SCREEN_HEIGHT)
        ])

        # 基础固定障碍物
        walls.extend([
            Wall(200, 200, 60, 60),
            Wall(SCREEN_WIDTH - 260, 200, 60, 60),
            Wall(200, SCREEN_HEIGHT - 260, 60, 60),
            Wall(SCREEN_WIDTH - 260, SCREEN_HEIGHT - 260, 60, 60),
        ])

        # 动态障碍物（这些会在游戏中变化）
        center_x, center_y = SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2
        time_factor = pygame.time.get_ticks() / 5000.0  # 5秒一个周期

        # 旋转的十字障碍物
        angle = time_factor * math.pi / 2  # 90度旋转
        cos_a, sin_a = math.cos(angle), math.sin(angle)

        # 计算旋转后的障碍物位置
        for i in range(4):
            base_angle = i * math.pi / 2
            x_offset = 80 * math.cos(base_angle + angle)
            y_offset = 80 * math.sin(base_angle + angle)

            wall_x = center_x + x_offset - 15
            wall_y = center_y + y_offset - 15
            walls.append(Wall(int(wall_x), int(wall_y), 30, 30))

        # 呼吸式障碍物
        breath_factor = 1 + 0.3 * math.sin(time_factor * 2 * math.pi)
        breath_size = int(40 * breath_factor)
        walls.extend([
            Wall(center_x - breath_size//2, 100, breath_size, 20),
            Wall(center_x - breath_size//2, SCREEN_HEIGHT - 120, breath_size, 20),
            Wall(100, center_y - breath_size//2, 20, breath_size),
            Wall(SCREEN_WIDTH - 120, center_y - breath_size//2, 20, breath_size),
        ])

        spawn_points = [
            Vector2(100, 100),
            Vector2(SCREEN_WIDTH - 100, SCREEN_HEIGHT - 100)
        ]

        return walls, spawn_points

class Player:
    def __init__(self, player_id: int, start_pos: Vector2, color: tuple):
        self.id = player_id
        self.pos = start_pos
        self.color = color
        self.radius = 20
        self.max_health = 100
        self.health = self.max_health
        self.max_armor = 50
        self.armor = 0
        self.speed = 200
        self.angle = 0

        # 武器系统
        self.weapons = [Weapon(WeaponType.PISTOL)]
        self.current_weapon_index = 0
        self.weapon = self.weapons[self.current_weapon_index]

        # 控制状态
        self.keys_pressed = set()
        self.mouse_pos = Vector2()

        # 游戏统计
        self.kills = 0
        self.deaths = 0

        # 无敌时间（复活后）
        self.invulnerable_time = 0
        self.invulnerable_duration = 2000  # 2秒无敌

        # 追踪状态
        self.has_tracking = False

        # 超级武器散射模式
        self.super_weapon_scatter_mode = False

    def take_damage(self, damage: int) -> bool:
        if self.invulnerable_time > 0:
            return False

        # 护甲先承受伤害
        if self.armor > 0:
            armor_damage = min(damage, self.armor)
            self.armor -= armor_damage
            damage -= armor_damage

        # 剩余伤害作用于血量
        self.health -= damage

        if self.health <= 0:
            self.health = 0
            return True  # 玩家死亡
        return False

    def heal(self, amount: int):
        self.health = min(self.max_health, self.health + amount)

    def add_armor(self, amount: int):
        self.armor = min(self.max_armor, self.armor + amount)

    def respawn(self, pos: Vector2):
        self.pos = pos
        self.health = self.max_health
        self.armor = 0
        self.invulnerable_time = pygame.time.get_ticks() + self.invulnerable_duration
        self.deaths += 1

    def switch_weapon(self, direction: int):
        if len(self.weapons) > 1:
            self.current_weapon_index = (self.current_weapon_index + direction) % len(self.weapons)
            self.weapon = self.weapons[self.current_weapon_index]

    def add_weapon(self, weapon_type: WeaponType):
        # 检查是否已有该武器
        for weapon in self.weapons:
            if weapon.type == weapon_type:
                weapon.current_ammo = weapon.stats.max_ammo  # 补满弹药
                return

        # 添加新武器
        self.weapons.append(Weapon(weapon_type))

    def update(self, dt: float, walls: List[Wall]):
        # 更新无敌时间
        current_time = pygame.time.get_ticks()
        if self.invulnerable_time > 0 and current_time > self.invulnerable_time:
            self.invulnerable_time = 0

        # 更新武器
        self.weapon.update()

        # 移动处理
        movement = Vector2()
        if 'up' in self.keys_pressed:
            movement.y -= 1
        if 'down' in self.keys_pressed:
            movement.y += 1
        if 'left' in self.keys_pressed:
            movement.x -= 1
        if 'right' in self.keys_pressed:
            movement.x += 1

        if movement.length() > 0:
            movement = movement.normalize()
            movement_delta = movement * self.speed * dt

            # 分别尝试X和Y方向的移动，实现沿墙滑动
            final_pos = Vector2(self.pos.x, self.pos.y)

            # 尝试X方向移动
            if movement_delta.x != 0:
                test_pos_x = Vector2(self.pos.x + movement_delta.x, self.pos.y)
                if self._can_move_to(test_pos_x, walls):
                    final_pos.x = test_pos_x.x

            # 尝试Y方向移动
            if movement_delta.y != 0:
                test_pos_y = Vector2(final_pos.x, self.pos.y + movement_delta.y)
                if self._can_move_to(test_pos_y, walls):
                    final_pos.y = test_pos_y.y

            self.pos = final_pos

    def _can_move_to(self, pos: Vector2, walls: List[Wall]) -> bool:
        """检查是否可以移动到指定位置"""
        # 检查边界
        if (pos.x - self.radius < 0 or pos.x + self.radius > SCREEN_WIDTH or
            pos.y - self.radius < 0 or pos.y + self.radius > SCREEN_HEIGHT):
            return False

        # 检查与墙壁的碰撞
        player_rect = pygame.Rect(pos.x - self.radius, pos.y - self.radius,
                                self.radius * 2, self.radius * 2)

        for wall in walls:
            if player_rect.colliderect(wall.rect):
                return False

        return True

    def shoot(self, target_pos: Vector2, target_player_id: int = -1) -> List[Bullet]:
        direction = target_pos - self.pos

        # 检查是否使用追踪
        use_tracking = self.has_tracking
        if use_tracking:
            self.has_tracking = False  # 一次性使用

        # 超级武器散射模式
        if (self.weapon.type == WeaponType.SUPER_WEAPON and
            self.super_weapon_scatter_mode):
            # 散射子弹使用手枪伤害，无传送功能
            bullets = []
            for i in range(12):
                angle = (i * 30) * math.pi / 180
                scatter_direction = Vector2(math.cos(angle), math.sin(angle))
                bullet = Bullet(Vector2(self.pos.x, self.pos.y), scatter_direction,
                              400, 25, self.id, is_tracking=use_tracking,
                              target_player_id=target_player_id)
                bullets.append(bullet)
            return bullets

        return self.weapon.shoot(self.pos, direction, self.id, use_tracking, target_player_id)

    def switch_to_weapon(self, weapon_index: int):
        """直接切换到指定索引的武器"""
        if 0 <= weapon_index < len(self.weapons):
            self.current_weapon_index = weapon_index
            self.weapon = self.weapons[self.current_weapon_index]

    def draw(self, screen):
        # 无敌时闪烁效果
        if self.invulnerable_time > 0:
            if (pygame.time.get_ticks() // 100) % 2:  # 每100ms闪烁一次
                return

        # 绘制玩家
        pygame.draw.circle(screen, self.color, self.pos.to_tuple(), self.radius)
        pygame.draw.circle(screen, WHITE, self.pos.to_tuple(), self.radius, 2)

        # 绘制方向指示器
        direction = self.mouse_pos - self.pos
        if direction.length() > 0:
            direction = direction.normalize()
            end_pos = self.pos + direction * (self.radius + 10)
            pygame.draw.line(screen, WHITE, self.pos.to_tuple(), end_pos.to_tuple(), 3)

    def get_rect(self):
        return pygame.Rect(self.pos.x - self.radius, self.pos.y - self.radius,
                          self.radius * 2, self.radius * 2)

class Particle:
    def __init__(self, pos: Vector2, velocity: Vector2, color: tuple, lifetime: int):
        self.pos = pos
        self.velocity = velocity
        self.color = color
        self.lifetime = lifetime
        self.birth_time = pygame.time.get_ticks()
        self.size = random.randint(2, 5)

    def update(self, dt: float):
        self.pos = self.pos + self.velocity * dt
        self.velocity = self.velocity * 0.98  # 阻力
        return pygame.time.get_ticks() - self.birth_time < self.lifetime

    def draw(self, screen):
        age_ratio = (pygame.time.get_ticks() - self.birth_time) / self.lifetime
        alpha = max(0, min(255, int(255 * (1 - age_ratio))))

        # 确保颜色值在有效范围内
        r = max(0, min(255, self.color[0]))
        g = max(0, min(255, self.color[1]))
        b = max(0, min(255, self.color[2]))

        # 创建临时surface用于alpha混合
        temp_surface = pygame.Surface((self.size * 2, self.size * 2), pygame.SRCALPHA)
        pygame.draw.circle(temp_surface, (r, g, b, alpha), (self.size, self.size), self.size)
        screen.blit(temp_surface, (self.pos.x - self.size, self.pos.y - self.size))

class Game:
    def __init__(self):
        # 高DPI支持
        import os
        os.environ['SDL_VIDEO_WINDOW_POS'] = 'centered'

        self.fullscreen = False
        self.screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
        pygame.display.set_caption("双人射击对战")
        self.clock = pygame.time.Clock()
        self.running = True

        # 地图管理器
        self.map_manager = MapManager()
        self.walls, spawn_points, self.current_map_name = self.map_manager.get_random_map()

        # 游戏对象
        self.players = [
            Player(0, spawn_points[0], RED),
            Player(1, spawn_points[1], BLUE)
        ]

        self.bullets = []
        self.power_ups = []
        self.particles = []

        # 连发状态
        self.player1_shooting = False
        self.player2_shooting = False

        # 动态地图更新时间
        self.last_dynamic_update = 0
        self.dynamic_update_interval = 100  # 100ms更新一次动态地图

        # 游戏状态
        self.game_over = False
        self.winner = None
        self.max_kills = 10  # 先到10杀获胜
        self.round_number = 1

        # 道具生成
        self.last_powerup_spawn = 0
        self.powerup_spawn_interval = 10000  # 10秒生成一个道具

        # 字体 - 支持中文
        self.font = self._get_chinese_font(36)
        self.small_font = self._get_chinese_font(24)

    def _get_chinese_font(self, size: int):
        """获取支持中文的字体"""
        # 尝试常见的中文字体
        chinese_fonts = [
            'SimHei',  # 黑体
            'Microsoft YaHei',  # 微软雅黑
            'SimSun',  # 宋体
            'KaiTi',   # 楷体
            'FangSong',  # 仿宋
            'Arial Unicode MS',  # Arial Unicode
        ]

        for font_name in chinese_fonts:
            try:
                font = pygame.font.SysFont(font_name, size)
                # 测试是否能正确渲染中文
                test_surface = font.render('测试', True, (255, 255, 255))
                if test_surface.get_width() > 0:
                    return font
            except:
                continue

        # 如果都不行，使用默认字体
        return pygame.font.Font(None, size)

    def change_map(self, map_name: str = None):
        """切换地图"""
        if map_name:
            self.walls, spawn_points, self.current_map_name = self.map_manager.get_map(map_name)
        else:
            self.walls, spawn_points, self.current_map_name = self.map_manager.get_random_map()

        # 重置玩家位置
        self.players[0].pos = spawn_points[0]
        self.players[1].pos = spawn_points[1]

        # 清除所有子弹和道具
        self.bullets.clear()
        self.power_ups.clear()
        self.particles.clear()

    def next_round(self):
        """开始下一轮游戏"""
        self.round_number += 1
        self.game_over = False
        self.winner = None

        # 重置玩家状态
        for player in self.players:
            player.health = player.max_health
            player.armor = 0
            player.kills = 0
            player.deaths = 0
            player.weapons = [Weapon(WeaponType.PISTOL)]
            player.current_weapon_index = 0
            player.weapon = player.weapons[0]

        # 切换到新地图
        self.change_map()

    def toggle_fullscreen(self):
        """切换全屏模式"""
        self.fullscreen = not self.fullscreen
        if self.fullscreen:
            self.screen = pygame.display.set_mode((0, 0), pygame.FULLSCREEN)
        else:
            self.screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))

    def spawn_powerup(self):
        # 随机选择道具类型
        power_type = random.choice(list(PowerUpType))

        # 找一个不与墙壁重叠的位置
        max_attempts = 50
        for _ in range(max_attempts):
            x = random.randint(50, SCREEN_WIDTH - 50)
            y = random.randint(50, SCREEN_HEIGHT - 50)
            pos = Vector2(x, y)

            # 检查是否与墙壁重叠
            temp_rect = pygame.Rect(x - 15, y - 15, 30, 30)
            collision = False
            for wall in self.walls:
                if temp_rect.colliderect(wall.rect):
                    collision = True
                    break

            if not collision:
                self.power_ups.append(PowerUp(pos, power_type))
                break

    def handle_events(self):
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                self.running = False

            elif event.type == pygame.KEYDOWN:
                # 玩家1控制 (WASD + QE切换武器 + 鼠标射击)
                if event.key == pygame.K_w:
                    self.players[0].keys_pressed.add('up')
                elif event.key == pygame.K_s:
                    self.players[0].keys_pressed.add('down')
                elif event.key == pygame.K_a:
                    self.players[0].keys_pressed.add('left')
                elif event.key == pygame.K_d:
                    self.players[0].keys_pressed.add('right')
                elif event.key == pygame.K_q:
                    self.players[0].switch_weapon(-1)
                elif event.key == pygame.K_e:
                    self.players[0].switch_weapon(1)
                elif event.key == pygame.K_r:
                    self.players[0].weapon.start_reload()

                # 玩家1快速切换武器 (1-6键)
                elif event.key == pygame.K_1:
                    self.players[0].switch_to_weapon(0)
                elif event.key == pygame.K_2:
                    self.players[0].switch_to_weapon(1)
                elif event.key == pygame.K_3:
                    self.players[0].switch_to_weapon(2)
                elif event.key == pygame.K_4:
                    self.players[0].switch_to_weapon(3)
                elif event.key == pygame.K_5:
                    self.players[0].switch_to_weapon(4)
                elif event.key == pygame.K_6:
                    self.players[0].switch_to_weapon(5)

                # 玩家1超级武器散射模式切换 (Z键)
                elif event.key == pygame.K_z:
                    if self.players[0].weapon.type == WeaponType.SUPER_WEAPON:
                        self.players[0].super_weapon_scatter_mode = not self.players[0].super_weapon_scatter_mode

                # 玩家2控制 (方向键 + 数字0射击 + 回车换武器 + +号装弹)
                elif event.key == pygame.K_UP:
                    self.players[1].keys_pressed.add('up')
                elif event.key == pygame.K_DOWN:
                    self.players[1].keys_pressed.add('down')
                elif event.key == pygame.K_LEFT:
                    self.players[1].keys_pressed.add('left')
                elif event.key == pygame.K_RIGHT:
                    self.players[1].keys_pressed.add('right')
                elif event.key == pygame.K_KP_PLUS or event.key == pygame.K_PLUS:
                    self.players[1].weapon.start_reload()
                elif event.key == pygame.K_RETURN:
                    self.players[1].switch_weapon(1)

                # 地图切换快捷键
                elif event.key == pygame.K_F1:
                    self.change_map("经典对战")
                elif event.key == pygame.K_F2:
                    self.change_map("迷宫战场")
                elif event.key == pygame.K_F3:
                    self.change_map("开放竞技场")
                elif event.key == pygame.K_F4:
                    self.change_map("十字路口")
                elif event.key == pygame.K_F5:
                    self.change_map("堡垒攻防")
                elif event.key == pygame.K_F6:
                    self.change_map("螺旋战场")
                elif event.key == pygame.K_F7:
                    self.change_map("变动战场")
                elif event.key == pygame.K_F12:
                    self.change_map()  # 随机地图

                # 新一轮游戏
                elif event.key == pygame.K_n and self.game_over:
                    self.next_round()

                # 全屏切换
                elif event.key == pygame.K_F11:
                    self.toggle_fullscreen()

            elif event.type == pygame.KEYUP:
                # 玩家1
                if event.key == pygame.K_w:
                    self.players[0].keys_pressed.discard('up')
                elif event.key == pygame.K_s:
                    self.players[0].keys_pressed.discard('down')
                elif event.key == pygame.K_a:
                    self.players[0].keys_pressed.discard('left')
                elif event.key == pygame.K_d:
                    self.players[0].keys_pressed.discard('right')

                # 玩家2
                elif event.key == pygame.K_UP:
                    self.players[1].keys_pressed.discard('up')
                elif event.key == pygame.K_DOWN:
                    self.players[1].keys_pressed.discard('down')
                elif event.key == pygame.K_LEFT:
                    self.players[1].keys_pressed.discard('left')
                elif event.key == pygame.K_RIGHT:
                    self.players[1].keys_pressed.discard('right')

            elif event.type == pygame.MOUSEBUTTONDOWN:
                if event.button == 1:  # 左键射击 (玩家1)
                    self.player1_shooting = True

            elif event.type == pygame.MOUSEBUTTONUP:
                if event.button == 1:  # 左键释放 (玩家1)
                    self.player1_shooting = False

        # 持续按键检测
        keys = pygame.key.get_pressed()

        # 玩家1射击 (鼠标左键)
        if self.player1_shooting:
            mouse_pos = Vector2(*pygame.mouse.get_pos())
            new_bullets = self.players[0].shoot(mouse_pos, 1)  # 目标是玩家2
            if new_bullets:
                self.bullets.extend(new_bullets)
                self.create_muzzle_flash(self.players[0].pos, self.players[0].weapon.type)

        # 玩家2射击 (仅数字0键) - 加特林不能连发
        if keys[pygame.K_0] or keys[pygame.K_KP0]:
            # 检查是否是加特林武器
            if self.players[1].weapon.type == WeaponType.GATLING:
                # 加特林不能连发，只能单发
                if not self.player2_shooting:
                    self.player2_shooting = True
                    target_pos = self.players[0].pos
                    new_bullets = self.players[1].shoot(target_pos, 0)  # 目标是玩家1
                    if new_bullets:
                        self.bullets.extend(new_bullets)
                        self.create_muzzle_flash(self.players[1].pos, self.players[1].weapon.type)
            else:
                # 其他武器正常射击
                target_pos = self.players[0].pos
                new_bullets = self.players[1].shoot(target_pos, 0)  # 目标是玩家1
                if new_bullets:
                    self.bullets.extend(new_bullets)
                    self.create_muzzle_flash(self.players[1].pos, self.players[1].weapon.type)
        else:
            self.player2_shooting = False

        # 更新玩家1鼠标位置
        self.players[0].mouse_pos = Vector2(*pygame.mouse.get_pos())

        # 更新玩家2朝向（朝向玩家1）
        self.players[1].mouse_pos = self.players[0].pos

    def create_muzzle_flash(self, pos: Vector2, weapon_type: WeaponType = WeaponType.PISTOL):
        # 根据武器类型调整粒子效果
        particle_count = 5
        colors = [ORANGE, YELLOW]

        if weapon_type == WeaponType.SHOTGUN:
            particle_count = 8
        elif weapon_type == WeaponType.GATLING:
            particle_count = 3
            colors = [RED, ORANGE]
        elif weapon_type == WeaponType.ONE_SHOT:
            particle_count = 12
            colors = [RED, WHITE]
        elif weapon_type == WeaponType.SUPER_WEAPON:
            particle_count = 10
            colors = [(255, 215, 0), WHITE]  # 金色

        for _ in range(particle_count):
            velocity = Vector2(
                random.uniform(-150, 150),
                random.uniform(-150, 150)
            )
            color = random.choice(colors)
            particle = Particle(Vector2(pos.x, pos.y), velocity, color, 250)
            self.particles.append(particle)

    def create_hit_effect(self, pos: Vector2):
        for _ in range(8):
            velocity = Vector2(
                random.uniform(-150, 150),
                random.uniform(-150, 150)
            )
            particle = Particle(Vector2(pos.x, pos.y), velocity, RED, 300)
            self.particles.append(particle)

    def create_super_weapon_explosion(self, pos: Vector2, owner_id: int):
        """创建super_weapon的爆炸效果，扩散12颗子弹"""
        # 创建爆炸粒子效果
        for _ in range(20):
            velocity = Vector2(
                random.uniform(-300, 300),
                random.uniform(-300, 300)
            )
            particle = Particle(Vector2(pos.x, pos.y), velocity, ORANGE, 500)
            self.particles.append(particle)

        # 扩散12颗子弹
        for i in range(12):
            angle = (i * 30) * math.pi / 180  # 每30度一颗子弹
            direction = Vector2(math.cos(angle), math.sin(angle))

            # 随机选择穿透或反弹子弹
            if random.choice([True, False]):
                # 穿透子弹
                bullet = Bullet(Vector2(pos.x, pos.y), direction, 400, 25, owner_id,
                              penetration=1, bullet_size=0.8)
            else:
                # 反弹子弹
                bullet = Bullet(Vector2(pos.x, pos.y), direction, 400, 25, owner_id,
                              bounce_count=2, bullet_size=0.8)

            # 设置5秒生命周期
            bullet.lifetime = 5000
            self.bullets.append(bullet)

    def update(self, dt: float):
        if self.game_over:
            return

        # 更新玩家
        for player in self.players:
            player.update(dt, self.walls)

        # 更新子弹
        self.bullets = [bullet for bullet in self.bullets if bullet.update(dt, self.players)]

        # 更新动态地图
        current_time = pygame.time.get_ticks()
        if (self.current_map_name == "变动战场" and
            current_time - self.last_dynamic_update > self.dynamic_update_interval):
            self.walls, _, _ = self.map_manager.get_map("变动战场")
            self.last_dynamic_update = current_time

        # 更新粒子
        self.particles = [particle for particle in self.particles if particle.update(dt)]

        # 更新道具
        self.power_ups = [powerup for powerup in self.power_ups if powerup.update(dt)]

        # 子弹碰撞检测
        for bullet in self.bullets[:]:
            bullet_rect = bullet.get_rect()

            # 与墙壁碰撞
            hit_wall = False
            for wall in self.walls:
                if bullet_rect.colliderect(wall.rect):
                    # 检查是否是边界墙（不可穿透）
                    is_boundary = (wall.rect.x <= 20 or wall.rect.y <= 20 or
                                 wall.rect.x >= SCREEN_WIDTH - 40 or wall.rect.y >= SCREEN_HEIGHT - 40)

                    if bullet.penetration > 0 and bullet.penetrated_walls < bullet.penetration and not is_boundary:
                        # 穿透墙壁
                        bullet.penetrated_walls += 1
                        self.create_hit_effect(bullet.pos)
                        # 穿透后继续移动，不移除子弹
                        break
                    elif bullet.bounce_count > 0 and bullet.bounces_used < bullet.bounce_count:
                        # 反弹
                        bullet.bounces_used += 1

                        # 改进的反弹逻辑
                        # 计算子弹与墙壁的碰撞面
                        bullet_prev_x = bullet.pos.x - bullet.velocity.x * 0.016
                        bullet_prev_y = bullet.pos.y - bullet.velocity.y * 0.016

                        # 判断碰撞的是哪个面
                        if (bullet_prev_x < wall.rect.left and bullet.pos.x >= wall.rect.left) or \
                           (bullet_prev_x > wall.rect.right and bullet.pos.x <= wall.rect.right):
                            bullet.velocity.x = -bullet.velocity.x  # 水平反弹
                        elif (bullet_prev_y < wall.rect.top and bullet.pos.y >= wall.rect.top) or \
                             (bullet_prev_y > wall.rect.bottom and bullet.pos.y <= wall.rect.bottom):
                            bullet.velocity.y = -bullet.velocity.y  # 垂直反弹
                        else:
                            # 角落碰撞，两个方向都反弹
                            bullet.velocity.x = -bullet.velocity.x
                            bullet.velocity.y = -bullet.velocity.y

                        # 将子弹移出墙壁
                        bullet.pos.x += bullet.velocity.x * 0.02
                        bullet.pos.y += bullet.velocity.y * 0.02

                        self.create_hit_effect(bullet.pos)

                        # 检查是否是super_weapon的最后一次反弹
                        if (bullet.bounces_used >= bullet.bounce_count and
                            hasattr(bullet, 'is_super_weapon') and bullet.is_super_weapon):
                            self.create_super_weapon_explosion(bullet.pos, bullet.owner_id)
                            self.bullets.remove(bullet)
                            hit_wall = True
                        break
                    else:
                        # 普通碰撞，移除子弹
                        self.bullets.remove(bullet)
                        self.create_hit_effect(bullet.pos)
                        hit_wall = True
                        break

            if hit_wall:
                continue
            else:
                # 与玩家碰撞
                for player in self.players:
                    if (player.id != bullet.owner_id and
                        bullet_rect.colliderect(player.get_rect())):

                        if player.take_damage(bullet.damage):
                            # 玩家死亡
                            death_pos = Vector2(player.pos.x, player.pos.y)
                            killer_pos = Vector2(self.players[bullet.owner_id].pos.x, self.players[bullet.owner_id].pos.y)

                            self.players[bullet.owner_id].kills += 1
                            self.create_hit_effect(player.pos)

                            # 检查是否是高强武器击杀
                            killer_weapon = self.players[bullet.owner_id].weapon
                            if killer_weapon.type == WeaponType.SUPER_WEAPON:
                                # 位置互换机制
                                self.players[bullet.owner_id].pos = death_pos
                                player.respawn(killer_pos)
                            else:
                                # 正常重生
                                spawn_pos = self.map_manager.spawn_points[player.id]
                                player.respawn(spawn_pos)

                            # 检查胜利条件
                            if self.players[bullet.owner_id].kills >= self.max_kills:
                                self.game_over = True
                                self.winner = bullet.owner_id
                        else:
                            self.create_hit_effect(bullet.pos)

                        self.bullets.remove(bullet)
                        break

        # 道具碰撞检测
        for powerup in self.power_ups[:]:
            for player in self.players:
                if powerup.get_rect().colliderect(player.get_rect()):
                    self.apply_powerup(player, powerup)
                    self.power_ups.remove(powerup)
                    break

        # 生成道具
        current_time = pygame.time.get_ticks()
        if current_time - self.last_powerup_spawn > self.powerup_spawn_interval:
            self.spawn_powerup()
            self.last_powerup_spawn = current_time

    def apply_powerup(self, player: Player, powerup: PowerUp):
        if powerup.type == PowerUpType.HEALTH:
            player.heal(30)
        elif powerup.type == PowerUpType.ARMOR:
            player.add_armor(25)
        elif powerup.type == PowerUpType.AMMO:
            player.weapon.current_ammo = player.weapon.stats.max_ammo
        elif powerup.type == PowerUpType.WEAPON_UPGRADE:
            # 武器概率分配
            normal_weapons = [WeaponType.SHOTGUN, WeaponType.MACHINE_GUN, WeaponType.SNIPER,
                            WeaponType.PENETRATOR, WeaponType.BOUNCER, WeaponType.ONE_SHOT, WeaponType.GATLING]
            special_weapons = [WeaponType.SUPER_WEAPON]

            # 高强武器概率为其他武器的1/3
            if random.random() < 0.25:  # 25%概率获得高强武器
                new_weapon = random.choice(special_weapons)
            else:
                new_weapon = random.choice(normal_weapons)

            player.add_weapon(new_weapon)

    def draw_ui(self):
        # 玩家1 UI (左上角)
        p1 = self.players[0]
        y_offset = 10

        # 血量条
        health_ratio = p1.health / p1.max_health
        health_width = 200
        health_rect = pygame.Rect(10, y_offset, health_width, 20)
        pygame.draw.rect(self.screen, DARK_GRAY, health_rect)
        pygame.draw.rect(self.screen, RED, pygame.Rect(10, y_offset, health_width * health_ratio, 20))
        pygame.draw.rect(self.screen, WHITE, health_rect, 2)

        # 护甲条
        if p1.armor > 0:
            y_offset += 25
            armor_ratio = p1.armor / p1.max_armor
            armor_rect = pygame.Rect(10, y_offset, health_width, 15)
            pygame.draw.rect(self.screen, DARK_GRAY, armor_rect)
            pygame.draw.rect(self.screen, BLUE, pygame.Rect(10, y_offset, health_width * armor_ratio, 15))
            pygame.draw.rect(self.screen, WHITE, armor_rect, 2)

        # 弹药信息
        y_offset += 30
        ammo_text = f"弹药: {p1.weapon.current_ammo}/{p1.weapon.stats.max_ammo}"
        if p1.weapon.is_reloading:
            ammo_text += " (装弹中...)"
        ammo_surface = self.small_font.render(ammo_text, True, WHITE)
        self.screen.blit(ammo_surface, (10, y_offset))

        # 武器信息
        y_offset += 25
        weapon_names = {
            WeaponType.PISTOL: "手枪",
            WeaponType.SHOTGUN: "霰弹枪",
            WeaponType.MACHINE_GUN: "机枪",
            WeaponType.SNIPER: "狙击枪",
            WeaponType.PENETRATOR: "穿透枪",
            WeaponType.BOUNCER: "反弹枪",
            WeaponType.ONE_SHOT: "高伤枪",
            WeaponType.GATLING: "加特林",
            WeaponType.SUPER_WEAPON: "超级武器"
        }
        weapon_text = f"武器: {weapon_names.get(p1.weapon.type, p1.weapon.type.value)}"
        weapon_surface = self.small_font.render(weapon_text, True, WHITE)
        self.screen.blit(weapon_surface, (10, y_offset))

        # 击杀数
        y_offset += 25
        kills_text = f"击杀: {p1.kills}"
        kills_surface = self.small_font.render(kills_text, True, WHITE)
        self.screen.blit(kills_surface, (10, y_offset))

        # 玩家2 UI (右上角)
        p2 = self.players[1]
        y_offset = 10

        # 血量条
        health_ratio = p2.health / p2.max_health
        health_rect = pygame.Rect(SCREEN_WIDTH - 210, y_offset, health_width, 20)
        pygame.draw.rect(self.screen, DARK_GRAY, health_rect)
        pygame.draw.rect(self.screen, RED, pygame.Rect(SCREEN_WIDTH - 210, y_offset, health_width * health_ratio, 20))
        pygame.draw.rect(self.screen, WHITE, health_rect, 2)

        # 护甲条
        if p2.armor > 0:
            y_offset += 25
            armor_ratio = p2.armor / p2.max_armor
            armor_rect = pygame.Rect(SCREEN_WIDTH - 210, y_offset, health_width, 15)
            pygame.draw.rect(self.screen, DARK_GRAY, armor_rect)
            pygame.draw.rect(self.screen, BLUE, pygame.Rect(SCREEN_WIDTH - 210, y_offset, health_width * armor_ratio, 15))
            pygame.draw.rect(self.screen, WHITE, armor_rect, 2)

        # 弹药信息
        y_offset += 30
        ammo_text = f"弹药: {p2.weapon.current_ammo}/{p2.weapon.stats.max_ammo}"
        if p2.weapon.is_reloading:
            ammo_text += " (装弹中...)"
        ammo_surface = self.small_font.render(ammo_text, True, WHITE)
        ammo_rect = ammo_surface.get_rect()
        ammo_rect.topright = (SCREEN_WIDTH - 10, y_offset)
        self.screen.blit(ammo_surface, ammo_rect)

        # 武器信息
        y_offset += 25
        weapon_text = f"武器: {weapon_names.get(p2.weapon.type, p2.weapon.type.value)}"
        weapon_surface = self.small_font.render(weapon_text, True, WHITE)
        weapon_rect = weapon_surface.get_rect()
        weapon_rect.topright = (SCREEN_WIDTH - 10, y_offset)
        self.screen.blit(weapon_surface, weapon_rect)

        # 击杀数
        y_offset += 25
        kills_text = f"击杀: {p2.kills}"
        kills_surface = self.small_font.render(kills_text, True, WHITE)
        kills_rect = kills_surface.get_rect()
        kills_rect.topright = (SCREEN_WIDTH - 10, y_offset)
        self.screen.blit(kills_surface, kills_rect)

    def draw(self):
        self.screen.fill(BLACK)

        # 绘制墙壁
        for wall in self.walls:
            wall.draw(self.screen)

        # 绘制道具
        for powerup in self.power_ups:
            powerup.draw(self.screen)

        # 绘制粒子
        for particle in self.particles:
            particle.draw(self.screen)

        # 绘制子弹
        for bullet in self.bullets:
            bullet.draw(self.screen)

        # 绘制玩家
        for player in self.players:
            player.draw(self.screen)

        # 绘制UI
        self.draw_ui()

        # 绘制地图信息和控制说明
        info_y = SCREEN_HEIGHT - 120

        # 当前地图信息
        map_text = f"当前地图: {self.current_map_name} (第{self.round_number}轮)"
        map_surface = self.small_font.render(map_text, True, YELLOW)
        self.screen.blit(map_surface, (10, info_y))

        # 控制说明
        controls_text = [
            "玩家1 (红色): WASD移动, 鼠标瞄准射击, R装弹, Q/E切换武器",
            "玩家2 (蓝色): 方向键移动, 数字0射击, +号装弹, 回车切换武器",
            "地图切换: F1-F6选择地图, F12随机地图, F11全屏",
            f"目标: 先达到{self.max_kills}击杀获胜!"
        ]

        for i, text in enumerate(controls_text):
            surface = self.small_font.render(text, True, WHITE)
            self.screen.blit(surface, (10, info_y + 25 + i * 20))

        # 游戏结束画面
        if self.game_over:
            overlay = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT))
            overlay.set_alpha(128)
            overlay.fill(BLACK)
            self.screen.blit(overlay, (0, 0))

            winner_text = f"玩家 {'1' if self.winner == 0 else '2'} 获胜!"
            winner_surface = self.font.render(winner_text, True, WHITE)
            winner_rect = winner_surface.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2 - 30))
            self.screen.blit(winner_surface, winner_rect)

            map_text = f"地图: {self.current_map_name}"
            map_surface = self.small_font.render(map_text, True, YELLOW)
            map_rect = map_surface.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2))
            self.screen.blit(map_surface, map_rect)

            controls_text = [
                "按 N 开始新一轮 (随机地图)",
                "按 F1-F6 选择特定地图开始新游戏",
                "按 ESC 退出游戏"
            ]

            for i, text in enumerate(controls_text):
                surface = self.small_font.render(text, True, WHITE)
                rect = surface.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2 + 40 + i * 25))
                self.screen.blit(surface, rect)

        pygame.display.flip()

    def run(self):
        while self.running:
            dt = self.clock.tick(FPS) / 1000.0  # 转换为秒

            self.handle_events()
            self.update(dt)
            self.draw()

            # 游戏结束时的额外控制
            if self.game_over:
                keys = pygame.key.get_pressed()
                if keys[pygame.K_ESCAPE]:
                    self.running = False

        pygame.quit()
        sys.exit()

def main():
    """
    双人同屏射击游戏

    游戏特色:
    - 双人对战，支持同屏游戏
    - 多种武器系统：手枪、霰弹枪、机枪、狙击枪
    - 道具系统：血包、护甲、弹药、武器升级
    - 6张不同风格的战术地图：经典对战、迷宫战场、开放竞技场、十字路口、堡垒攻防、螺旋战场
    - 随机地图系统，每轮自动切换
    - 平衡机制：血量、护甲、弹药管理
    - 视觉效果：粒子系统、UI显示
    - 中文界面支持

    控制方式:
    玩家1 (红色):
    - WASD: 移动
    - 鼠标: 瞄准和射击
    - R: 装弹
    - Q/E: 切换武器

    玩家2 (蓝色):
    - 方向键: 移动
    - 数字0: 射击 (自动瞄准玩家1)
    - +号: 装弹
    - 回车: 切换武器

    地图切换:
    - F1: 经典对战
    - F2: 迷宫战场
    - F3: 开放竞技场
    - F4: 十字路口
    - F5: 堡垒攻防
    - F6: 螺旋战场
    - F12: 随机地图
    - F11: 全屏切换
    - N: 新一轮 (游戏结束后)

    武器系统:
    - 基础武器: 手枪、霰弹枪、机枪、狙击枪
    - 特殊武器: 穿透枪(绿色子弹)、反弹枪(紫色子弹)、高伤枪(红色大子弹)、加特林
    - 高强武器: 击杀后与敌人位置互换的特殊武器

    获胜条件: 先达到10击杀的玩家获胜
    """
    game = Game()
    game.run()

if __name__ == "__main__":
    main()
