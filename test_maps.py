#!/usr/bin/env python3
"""
地图测试脚本 - 验证所有地图的出生点是否安全
"""

import pygame
import sys
from dual_shooter_game import MapManager, SCREEN_WIDTH, SCREEN_HEIGHT

def test_spawn_points():
    """测试所有地图的出生点安全性"""
    pygame.init()
    
    map_manager = MapManager()
    player_radius = 20
    
    print("🗺️  测试所有地图的出生点安全性...")
    print("=" * 50)
    
    for map_name in map_manager.maps.keys():
        print(f"\n📍 测试地图: {map_name}")
        
        walls, spawn_points, _ = map_manager.get_map(map_name)
        
        for i, spawn_point in enumerate(spawn_points):
            player_rect = pygame.Rect(
                spawn_point.x - player_radius, 
                spawn_point.y - player_radius,
                player_radius * 2, 
                player_radius * 2
            )
            
            # 检查边界
            if (spawn_point.x - player_radius < 0 or 
                spawn_point.x + player_radius > SCREEN_WIDTH or
                spawn_point.y - player_radius < 0 or 
                spawn_point.y + player_radius > SCREEN_HEIGHT):
                print(f"  ❌ 玩家{i+1} 出生点超出边界: ({spawn_point.x}, {spawn_point.y})")
                continue
            
            # 检查墙壁碰撞
            collision = False
            for wall in walls:
                if player_rect.colliderect(wall.rect):
                    collision = True
                    print(f"  ❌ 玩家{i+1} 出生点与墙壁重叠: ({spawn_point.x}, {spawn_point.y})")
                    print(f"     墙壁位置: ({wall.rect.x}, {wall.rect.y}, {wall.rect.width}, {wall.rect.height})")
                    break
            
            if not collision:
                print(f"  ✅ 玩家{i+1} 出生点安全: ({spawn_point.x}, {spawn_point.y})")
    
    print("\n" + "=" * 50)
    print("✅ 地图测试完成！")
    
    pygame.quit()

if __name__ == "__main__":
    test_spawn_points()
