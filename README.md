# 双人同屏射击游戏

一个刺激好玩且平衡的双人同屏射击游戏，使用Python和Pygame开发。

## 游戏特色

- **双人对战**: 支持同屏双人游戏
- **多种武器系统**: 手枪、霰弹枪、机枪、狙击枪，各有特色
- **道具系统**: 血包、护甲、弹药补给、武器升级
- **6张战术地图**: 
  - 经典对战: 平衡的对称地图
  - 迷宫战场: 复杂的迷宫布局
  - 开放竞技场: 少量掩体的开放战场
  - 十字路口: 十字形通道设计
  - 堡垒攻防: 不对称的堡垒地图
  - 螺旋战场: 螺旋形通道设计
- **随机地图系统**: 每轮自动切换地图
- **平衡机制**: 血量、护甲、弹药管理
- **视觉效果**: 粒子系统、UI显示
- **中文界面**: 完整的中文支持

## 安装和运行

1. 确保安装了Python 3.7+
2. 安装依赖：
   ```bash
   pip install pygame
   ```
3. 运行游戏：
   ```bash
   python dual_shooter_game.py
   ```

## 控制方式

### 玩家1 (红色)
- **WASD**: 移动
- **鼠标**: 瞄准和射击
- **R**: 装弹
- **Q/E**: 切换武器

### 玩家2 (蓝色)
- **方向键**: 移动
- **数字0**: 射击 (自动瞄准玩家1)
- **+号**: 装弹
- **回车**: 切换武器

### 地图切换
- **F1**: 经典对战
- **F2**: 迷宫战场
- **F3**: 开放竞技场
- **F4**: 十字路口
- **F5**: 堡垒攻防
- **F6**: 螺旋战场
- **F12**: 随机地图
- **F11**: 全屏切换
- **N**: 新一轮 (游戏结束后)

## 武器系统

### 基础武器
| 武器 | 伤害 | 射速 | 弹药 | 特点 |
|------|------|------|------|------|
| 手枪 | 25 | 中等 | 12 | 平衡的基础武器 |
| 霰弹枪 | 15×5 | 慢 | 8 | 近距离高伤害，散射 |
| 机枪 | 20 | 快 | 30 | 高射速，持续火力 |
| 狙击枪 | 80 | 很慢 | 5 | 高精度，高伤害 |

### 特殊武器
| 武器 | 伤害 | 特点 | 子弹颜色 |
|------|------|------|----------|
| 穿透枪 | 28 | 可穿透1-2层墙壁(不可穿透边界) | 绿色 |
| 反弹枪 | 30 | 可反弹2-4次 | 紫色 |
| 高伤枪 | 100 | 一击必杀，7秒装弹 | 红色大子弹 |
| 加特林 | 8 | 300发弹药，玩家1可连发，玩家2单发 | 黄色 |
| 超级武器 | 50 | 穿透+反弹+击杀位置互换+爆炸扩散 | 金色闪烁 |

## 道具系统

- **血包** (红色): 恢复30点生命值
- **护甲** (蓝色): 增加25点护甲值
- **弹药** (绿色): 补满当前武器弹药
- **武器升级** (紫色): 随机获得高级武器

## 游戏规则

- 先达到10击杀的玩家获胜
- 玩家死亡后会在出生点重生
- 重生后有2秒无敌时间
- 道具每10秒随机生成一个
- 道具30秒后自动消失

## 地图介绍

### 经典对战
平衡的对称地图，适合新手练习，有中央十字掩体和四角掩体。

### 迷宫战场
复杂的迷宫布局，考验玩家的走位和战术意识。

### 开放竞技场
开放式战场，少量掩体，更考验射击技巧和反应速度。

### 十字路口
十字形通道设计，四个房间连接，战术性强。

### 堡垒攻防
不对称地图，两个堡垒设计，适合攻防战术。

### 螺旋战场
螺旋形通道，独特的地形设计，增加游戏趣味性。

## 新功能亮点

### 🔫 超级武器系统
- **超级武器**: 金色闪烁子弹，既能穿透又能反弹
- **位置互换**: 击杀敌人后瞬间交换位置
- **爆炸扩散**: 反弹结束后扩散12颗随机特性子弹

### 🎯 改进的射击系统
- **穿透机制**: 绿色子弹可穿透内部墙壁(不可穿透边界)
- **反弹机制**: 紫色子弹智能反弹，角度更真实
- **连发射击**: 玩家1可用加特林连发，玩家2限制单发平衡性

### 🎨 视觉增强
- **子弹颜色**: 不同武器子弹有独特颜色和效果
- **粒子效果**: 根据武器类型显示不同的枪口火焰
- **道具标识**: 道具有清晰的符号标识(+, A, ∞, W)

## 技巧提示

1. **合理使用掩体**: 利用墙壁和障碍物保护自己
2. **武器选择**: 根据地图和战况选择合适的武器
3. **弹药管理**: 及时装弹，避免在战斗中弹尽粮绝
4. **道具争夺**: 积极争夺地图上的道具
5. **走位技巧**: 利用地形优势，避免被包围
6. **特殊武器**: 善用穿透和反弹特性制造意外击杀
7. **超级武器**: 获得超级武器后可主动寻求击杀以利用位置互换

享受游戏吧！🎮
